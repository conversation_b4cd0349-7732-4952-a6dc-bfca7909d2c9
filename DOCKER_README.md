# Buzfi Laravel Project - Docker Setup with <PERSON><PERSON>

This project uses <PERSON><PERSON> for Docker containerization, providing a complete development environment with PHP 8.2, Nginx, MySQL, Redis, phpMyAdmin, and Redis Commander.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed on your system
- Git (for cloning)
- Make (optional, for using Makefile shortcuts)

### 1. Automated Setup (Recommended)

```bash
# Run the automated setup script
./setup-project.sh
```

This script will:
- Set up environment file
- Build Docker containers
- Start all services
- Install dependencies
- Generate application key
- Set up storage links
- Run migrations
- Configure permissions

### 2. Manual Setup (Alternative)

```bash
# Copy environment file
cp .env.docker .env

# Use Makefile for setup
make setup
```

### 2. Manual Setup (if Make is not available)

```bash
# Navigate to Laradock directory
cd docker/laradock

# Build containers
docker-compose build nginx php-fpm mysql redis phpmyadmin redis-commander

# Start containers
docker-compose up -d nginx php-fpm mysql redis phpmyadmin redis-commander

# Install dependencies
docker-compose exec php-fpm composer install

# Generate application key
docker-compose exec php-fpm php artisan key:generate

# Create storage link
docker-compose exec php-fpm php artisan storage:link

# Run migrations
docker-compose exec php-fpm php artisan migrate
```

## 🌐 Access URLs

After starting the containers, you can access:

- **Application**: http://localhost/buzfi-new-backend
- **phpMyAdmin**: http://localhost:8080
  - Username: `root`
  - Password: `root`
- **Redis Commander**: http://localhost:8081
  - Username: `admin`
  - Password: `admin`

## 📊 Database Information

- **Host**: localhost:3306
- **Database**: buzfi
- **Username**: buzfi_user
- **Password**: buzfi_password
- **Root Password**: root

The `buzfi.sql` file from `import_database/` folder is automatically imported when MySQL container starts for the first time.

## 🔧 Available Make Commands

### Project Management
```bash
make start          # Start all containers
make stop           # Stop all containers
make restart        # Restart all containers
make build          # Build containers
make rebuild        # Rebuild containers from scratch
```

### Development
```bash
make shell          # Access PHP container shell
make install        # Install dependencies
make setup          # Complete project setup
```

### Laravel Commands
```bash
make artisan cmd="migrate"              # Run artisan commands
make composer cmd="install"             # Run composer commands
make migrate                            # Run migrations
make migrate-fresh                      # Fresh migration with seeding
make cache-clear                        # Clear all caches
make optimize                           # Optimize application
```

### Database Management
```bash
make db-import      # Import buzfi.sql database
make db-backup      # Backup database
make db-shell       # Access MySQL shell
```

### Monitoring
```bash
make logs           # Show all container logs
make logs-nginx     # Show Nginx logs
make logs-php       # Show PHP-FPM logs
make logs-mysql     # Show MySQL logs
make status         # Show container status
make info           # Show project information
```

### Cleanup
```bash
make clean          # Clean up Docker resources
make clean-all      # Clean up everything including images
```

## 🐳 Alternative Management Scripts

### Using docker-start.sh Script
```bash
./docker-start.sh start      # Start containers
./docker-start.sh stop       # Stop containers
./docker-start.sh restart    # Restart containers
./docker-start.sh status     # Show status
./docker-start.sh logs       # Show logs
./docker-start.sh shell      # Access PHP shell
./docker-start.sh artisan migrate  # Run artisan commands
```

### Manual Docker Commands

If you prefer using Docker Compose directly:

```bash
cd docker/laradock

# Start services
docker-compose up -d nginx php-fpm mysql redis phpmyadmin redis-commander

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Access PHP container
docker-compose exec php-fpm bash

# Run Laravel commands
docker-compose exec php-fpm php artisan migrate
docker-compose exec php-fpm composer install
```

## 📁 Project Structure

```
buzfi-new-backend/
├── docker/
│   ├── laradock/           # Laradock installation
│   ├── mysql/              # MySQL configurations
│   ├── nginx/              # Nginx configurations
│   ├── php/                # PHP configurations
│   └── redis/              # Redis configurations
├── import_database/
│   └── buzfi.sql          # Database dump for import
├── .env.docker            # Docker environment configuration
├── Makefile               # Project management shortcuts
└── DOCKER_README.md       # This file
```

## 🔧 Configuration Details

### PHP 8.2
- All required Laravel extensions installed
- Redis extension enabled
- Custom php.ini with optimized settings

### Nginx
- Custom configuration for `/buzfi-new-backend` path
- Gzip compression enabled
- Security headers configured

### MySQL 8.0
- Database: `buzfi`
- Automatic import of `buzfi.sql` on first run
- phpMyAdmin for web-based management

### Redis
- Used for caching and sessions
- Redis Commander for web-based management
- Password protected

## 🚨 Troubleshooting

### Container Issues
```bash
# Check container status
make status

# View logs
make logs

# Restart containers
make restart
```

### Database Issues
```bash
# Re-import database
make db-import

# Access database shell
make db-shell
```

### Permission Issues
```bash
# Fix storage permissions
make shell
chmod -R 775 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### Cache Issues
```bash
# Clear all caches
make cache-clear

# Optimize application
make optimize
```

## 🔄 Starting and Stopping the Project

### To Start the Project:
```bash
make start
```
This will start all necessary containers and the application will be available at http://localhost/buzfi-new-backend

### To Stop the Project:
```bash
make stop
```
This will stop all containers but preserve data.

### To Completely Reset:
```bash
make clean-all
make setup
```
This will remove all containers and data, then set up everything fresh.

## 📝 Notes

1. The first startup may take longer as Docker needs to build images and import the database
2. Database data is persisted in Docker volumes
3. The application code is mounted from the host, so changes are reflected immediately
4. All logs are available through the `make logs` commands
5. Use `make info` to see all access URLs and credentials

## 🆘 Support

If you encounter any issues:
1. Check container status: `make status`
2. View logs: `make logs`
3. Try restarting: `make restart`
4. For complete reset: `make clean-all && make setup`
