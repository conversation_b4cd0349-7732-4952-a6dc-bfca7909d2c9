; php options

pid = /var/run/hhvm/pid

; hhvm specific
hhvm.server.port = 9000
hhvm.server.type = fastcgi
hhvm.server.default_document = index.php
hhvm.server.error_document404 = index.php
hhvm.server.upload.upload_max_file_size = 25M
hhvm.log.level = Error
hhvm.log.header = true
hhvm.log.access[default][file] = /var/log/hhvm/access.log
hhvm.log.access[default][format] = "%h %l %u %t \"%r\" %>s %b"
hhvm.server.source_root=/var/www/public
hhvm.repo.central.path = /var/run/hhvm/hhvm.hhbc

; Uncomment to log to files instead of STDOUT
;hhvm.log.use_log_file = true
;hhvm.log.file = /var/log/hhvm/error.log
