/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
   --ifm-color-primary: #996ee1;
  --ifm-color-primary-dark: #724EB5;
  --ifm-color-primary-darker: #6745A8;
  --ifm-color-primary-darkest: #5C3C9B;
  --ifm-color-primary-light: #8A63CF;
  --ifm-color-primary-lighter: #9770DC;
  --ifm-color-primary-lightest: #A47DE9;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #FAAE19;
  --ifm-color-primary-dark: #E09D17;
  --ifm-color-primary-darker: #C68C15;
  --ifm-color-primary-darkest: #AC7B13;
  --ifm-color-primary-light: #FBB43D;
  --ifm-color-primary-lighter: #FCC961;
  --ifm-color-primary-lightest: #FDDC85;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}
