{"name": "laradock-documentation", "version": "1.0.0", "private": false, "scripts": {"start": "docusaurus start", "deploy": "docusaurus build && gh-pages -d build", "docusaurus": "<PERSON>cusaurus", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@sista/ai-assistant-react": "^3.3.21", "@docusaurus/core": "3.3.0", "@docusaurus/preset-classic": "3.3.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.3.0", "@docusaurus/tsconfig": "3.3.0", "@docusaurus/types": "3.3.0", "gh-pages": "^6.1.1", "typescript": "~5.2.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}