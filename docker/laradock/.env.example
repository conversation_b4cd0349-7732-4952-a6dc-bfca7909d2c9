###########################################################
###################### General Setup ######################
###########################################################

### Paths #################################################

# Point to the path of your applications code on your host
APP_CODE_PATH_HOST=../

# Point to where the `APP_CODE_PATH_HOST` should be in the container
APP_CODE_PATH_CONTAINER=/var/www

# You may add flags to the path `:cached`, `:delegated`. When using Docker Sync add `:nocopy`
APP_CODE_CONTAINER_FLAG=:cached

# Choose storage path on your machine. For all storage systems
DATA_PATH_HOST=~/.laradock/data

### Drivers ################################################

# All volumes driver
VOLUMES_DRIVER=local

# All Networks driver
NETWORKS_DRIVER=bridge

### Docker compose files ##################################

# Select which docker-compose files to include. If using docker-sync append `:docker-compose.sync.yml` at the end
COMPOSE_FILE=docker-compose.yml

# Change the separator from : to ; on Windows
COMPOSE_PATH_SEPARATOR=:

# Define the prefix of container names. This is useful if you have multiple projects that use laradock to have separate containers per project.
COMPOSE_PROJECT_NAME=laradock

### PHP Version ###########################################

# Select a PHP version of the Workspace and PHP-FPM containers (Does not apply to HHVM).
# Accepted values: 8.4 - 8.3 - 8.2 - 8.1 - 8.0 - 7.4 - 7.3 - 7.2 - 7.1 - 7.0 - 5.6
PHP_VERSION=8.3

### Phalcon Version ###########################################

# Select a Phalcon version of the Workspace and PHP-FPM containers (Does not apply to HHVM). Accepted values: 5.0.0+
PHALCON_VERSION=5.0.0

### PHP Interpreter #######################################

# Select the PHP Interpreter. Accepted values: hhvm - php-fpm
PHP_INTERPRETER=php-fpm

### Docker Host IP ########################################

# Enter your Docker Host IP (will be appended to /etc/hosts). Default is `*********`
DOCKER_HOST_IP=*********

### Remote Interpreter ####################################

# Choose a Remote Interpreter entry matching name. Default is `laradock`
PHP_IDE_CONFIG=serverName=laradock

### PHP USE LEGACY OPENSSL ################################

# Since OpenSSL 3 some ciphers are not available
PHP_LEGACY_OPENSSL=false

### PHP DOWNGRADEOPENSSL TLS AND SECLEVEL #################

PHP_DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL=false

# Accepted values: 1.2 - 1.1 - 1.0
PHP_DOWNGRADE_OPENSSL_TLS_VERSION=1.2

### Windows Path ##########################################

# A fix for Windows users, to ensure the application path works
COMPOSE_CONVERT_WINDOWS_PATHS=1

### Environment ###########################################

# If you need to change the sources (i.e. to China), set CHANGE_SOURCE to true
CHANGE_SOURCE=false
# Set ORACLE INSTANT_CLIENT_MIRROR option if you want to use Intranet improve download, you can download files first
ORACLE_INSTANT_CLIENT_MIRROR=https://github.com/the-paulus/oracle-instantclient/raw/master/
ORACLE_INSTANT_CLIENT_ARCH=x86_64
ORACLE_INSTANT_CLIENT_MAJOR=18
ORACLE_INSTANT_CLIENT_MINOR=3

### Docker Sync ###########################################

# If you are using Docker Sync. For `osx` use 'native_osx', for `windows` use 'unison', for `linux` docker-sync is not required
DOCKER_SYNC_STRATEGY=native_osx

### Install Oh My ZSH! ####################################

# If you want to use "Oh My ZSH!" with Laravel autocomplete plugin, set SHELL_OH_MY_ZSH to true.

SHELL_OH_MY_ZSH=false
SHELL_OH_MY_ZSH_AUTOSUGESTIONS=false
SHELL_OH_MY_ZSH_ALIASES=false

###########################################################
################ Containers Customization #################
###########################################################

### WORKSPACE #############################################

WORKSPACE_BASE_IMAGE_TAG_PREFIX=latest
WORKSPACE_COMPOSER_GLOBAL_INSTALL=true
WORKSPACE_COMPOSER_VERSION=2
WORKSPACE_COMPOSER_AUTH_JSON=false
WORKSPACE_COMPOSER_REPO_PACKAGIST=
WORKSPACE_NVM_NODEJS_ORG_MIRROR=
WORKSPACE_INSTALL_NODE=true
WORKSPACE_NODE_VERSION=node
WORKSPACE_NPM_REGISTRY=
WORKSPACE_NPM_FETCH_RETRIES=2
WORKSPACE_NPM_FETCH_RETRY_FACTOR=10
WORKSPACE_NPM_FETCH_RETRY_MINTIMEOUT=10000
WORKSPACE_NPM_FETCH_RETRY_MAXTIMEOUT=60000
WORKSPACE_INSTALL_PNPM=false
WORKSPACE_INSTALL_YARN=true
WORKSPACE_YARN_VERSION=latest
WORKSPACE_INSTALL_NPM_GULP=true
WORKSPACE_INSTALL_NPM_BOWER=false
WORKSPACE_INSTALL_NPM_VUE_CLI=true
WORKSPACE_INSTALL_NPM_ANGULAR_CLI=false
WORKSPACE_INSTALL_NPM_CHECK_UPDATES_CLI=false
WORKSPACE_INSTALL_POPPLER_UTILS=false
WORKSPACE_INSTALL_PHPREDIS=true
WORKSPACE_INSTALL_WORKSPACE_SSH=false
WORKSPACE_INSTALL_SUBVERSION=false
WORKSPACE_INSTALL_BZ2=false
WORKSPACE_INSTALL_GMP=false
WORKSPACE_INSTALL_GNUPG=false
WORKSPACE_INSTALL_XDEBUG=false
WORKSPACE_INSTALL_PCOV=false
WORKSPACE_INSTALL_PHPDBG=false
WORKSPACE_INSTALL_SSH2=false
WORKSPACE_INSTALL_LDAP=false
WORKSPACE_INSTALL_SOAP=false
WORKSPACE_INSTALL_XSL=false
WORKSPACE_INSTALL_SMB=false
WORKSPACE_INSTALL_IMAP=false
WORKSPACE_INSTALL_MONGO=false
WORKSPACE_INSTALL_AMQP=false
WORKSPACE_INSTALL_CASSANDRA=false
WORKSPACE_INSTALL_ZMQ=false
WORKSPACE_INSTALL_GEARMAN=false
WORKSPACE_INSTALL_MSSQL=false
WORKSPACE_INSTALL_DRUSH=false
WORKSPACE_DRUSH_VERSION=8.4.6
WORKSPACE_INSTALL_DRUPAL_CONSOLE=false
WORKSPACE_INSTALL_WP_CLI=false
WORKSPACE_INSTALL_AEROSPIKE=false
WORKSPACE_INSTALL_OCI8=false
WORKSPACE_INSTALL_V8JS=false
WORKSPACE_INSTALL_LARAVEL_ENVOY=false
WORKSPACE_INSTALL_LARAVEL_INSTALLER=false
WORKSPACE_INSTALL_XLSWRITER=false
WORKSPACE_INSTALL_DEPLOYER=false
WORKSPACE_INSTALL_PRESTISSIMO=false
WORKSPACE_INSTALL_LINUXBREW=false
WORKSPACE_INSTALL_MC=false
WORKSPACE_INSTALL_SYMFONY=false
WORKSPACE_INSTALL_PYTHON=false
WORKSPACE_INSTALL_PYTHON3=false
WORKSPACE_INSTALL_POWERLINE=false
WORKSPACE_INSTALL_SUPERVISOR=false
WORKSPACE_INSTALL_IMAGE_OPTIMIZERS=false
WORKSPACE_INSTALL_IMAGEMAGICK=false
WORKSPACE_IMAGEMAGICK_VERSION=latest
WORKSPACE_INSTALL_TERRAFORM=false
WORKSPACE_INSTALL_DUSK_DEPS=false
WORKSPACE_INSTALL_PG_CLIENT=false
WORKSPACE_INSTALL_PHALCON=false
WORKSPACE_INSTALL_SWOOLE=false
WORKSPACE_INSTALL_TAINT=false
WORKSPACE_INSTALL_LIBPNG=false
WORKSPACE_INSTALL_GRAPHVIZ=false
WORKSPACE_INSTALL_IONCUBE=false # PHP 8.2 is not supported yet.
WORKSPACE_INSTALL_MYSQL_CLIENT=false
WORKSPACE_INSTALL_PING=false
WORKSPACE_INSTALL_SSHPASS=false
WORKSPACE_INSTALL_INOTIFY=false
WORKSPACE_INSTALL_FSWATCH=false
WORKSPACE_INSTALL_YAML=false
WORKSPACE_INSTALL_RDKAFKA=false
WORKSPACE_INSTALL_MAILPARSE=false
WORKSPACE_INSTALL_XMLRPC=false
WORKSPACE_INSTALL_APCU=false
WORKSPACE_PUID=1000
WORKSPACE_PGID=1000
WORKSPACE_CHROME_DRIVER_VERSION=2.42
WORKSPACE_TIMEZONE=UTC
WORKSPACE_SSH_PORT=2222
WORKSPACE_INSTALL_FFMPEG=false
WORKSPACE_INSTALL_AUDIOWAVEFORM=false
WORKSPACE_INSTALL_WKHTMLTOPDF=false
WORKSPACE_WKHTMLTOPDF_VERSION=********-3
WORKSPACE_INSTALL_GNU_PARALLEL=false
WORKSPACE_INSTALL_AST=true
WORKSPACE_AST_VERSION=1.0.10
WORKSPACE_BROWSERSYNC_HOST_PORT=3000
WORKSPACE_BROWSERSYNC_UI_HOST_PORT=3001
WORKSPACE_VUE_CLI_SERVE_HOST_PORT=8080
WORKSPACE_VUE_CLI_UI_HOST_PORT=8001
WORKSPACE_ANGULAR_CLI_SERVE_HOST_PORT=4200
WORKSPACE_INSTALL_GIT_PROMPT=false
WORKSPACE_INSTALL_DOCKER_CLIENT=false
WORKSPACE_INSTALL_LNAV=false
WORKSPACE_INSTALL_PROTOC=false
WORKSPACE_INSTALL_PHPDECIMAL=false
WORKSPACE_INSTALL_ZOOKEEPER=false
WORKSPACE_INSTALL_SSDB=false
WORKSPACE_INSTALL_TRADER=false
WORKSPACE_PROTOC_VERSION=latest
WORKSPACE_INSTALL_MEMCACHED=true
WORKSPACE_INSTALL_EVENT=false
WORKSPACE_INSTALL_DNSUTILS=true
WORKSPACE_XDEBUG_PORT=9000
WORKSPACE_VITE_PORT=5173
WORKSPACE_INSTALL_JDK=true
WORKSPACE_INSTALL_GITHUB_CLI=false

### PHP_FPM ###############################################

PHP_FPM_BASE_IMAGE_TAG_PREFIX=latest
PHP_FPM_INSTALL_BCMATH=true
PHP_FPM_INSTALL_MYSQLI=true
PHP_FPM_INSTALL_INTL=true
PHP_FPM_INSTALL_IMAGEMAGICK=true
PHP_FPM_IMAGEMAGICK_VERSION=latest
PHP_FPM_INSTALL_OPCACHE=true
PHP_FPM_INSTALL_IMAGE_OPTIMIZERS=true
PHP_FPM_INSTALL_PHPREDIS=true
PHP_FPM_INSTALL_MEMCACHED=false
PHP_FPM_INSTALL_BZ2=false
PHP_FPM_INSTALL_ENCHANT=false
PHP_FPM_INSTALL_GMP=false
PHP_FPM_INSTALL_GNUPG=false
PHP_FPM_INSTALL_XDEBUG=false
PHP_FPM_INSTALL_PCOV=false
PHP_FPM_INSTALL_XHPROF=false
PHP_FPM_INSTALL_PHPDBG=false
PHP_FPM_INSTALL_SMB=false
PHP_FPM_INSTALL_IMAP=false
PHP_FPM_INSTALL_MONGO=false
PHP_FPM_INSTALL_AMQP=false
PHP_FPM_INSTALL_CASSANDRA=false
PHP_FPM_INSTALL_ZMQ=false
PHP_FPM_INSTALL_GEARMAN=false
PHP_FPM_INSTALL_MSSQL=false
PHP_FPM_INSTALL_SSH2=false
PHP_FPM_INSTALL_SOAP=false
PHP_FPM_INSTALL_XSL=false
PHP_FPM_INSTALL_EXIF=false
PHP_FPM_INSTALL_AEROSPIKE=false
PHP_FPM_INSTALL_OCI8=false
PHP_FPM_INSTALL_PGSQL=false
PHP_FPM_INSTALL_GHOSTSCRIPT=false
PHP_FPM_INSTALL_LDAP=false
PHP_FPM_INSTALL_PHALCON=false
PHP_FPM_INSTALL_SWOOLE=false
PHP_FPM_INSTALL_TAINT=false
PHP_FPM_INSTALL_PG_CLIENT=false
PHP_FPM_INSTALL_POSTGIS=false
PHP_FPM_INSTALL_PCNTL=false
PHP_FPM_INSTALL_CALENDAR=false
PHP_FPM_INSTALL_FAKETIME=false
PHP_FPM_INSTALL_IONCUBE=false # PHP 8.2 is not supported yet.
PHP_FPM_INSTALL_RDKAFKA=false
PHP_FPM_INSTALL_GETTEXT=false
PHP_FPM_INSTALL_XMLRPC=false
PHP_FPM_FAKETIME=-0
PHP_FPM_INSTALL_APCU=false
PHP_FPM_INSTALL_CACHETOOL=false
PHP_FPM_INSTALL_YAML=false
PHP_FPM_INSTALL_ADDITIONAL_LOCALES=false
PHP_FPM_INSTALL_MYSQL_CLIENT=false
PHP_FPM_INSTALL_PING=false
PHP_FPM_INSTALL_SSHPASS=false
PHP_FPM_INSTALL_MAILPARSE=false
PHP_FPM_INSTALL_WKHTMLTOPDF=false
PHP_FPM_WKHTMLTOPDF_VERSION=********-3
PHP_FPM_INSTALL_XLSWRITER=false
PHP_FPM_INSTALL_PHPDECIMAL=false
PHP_FPM_INSTALL_ZOOKEEPER=false
PHP_FPM_INSTALL_SSDB=false
PHP_FPM_INSTALL_TRADER=false
PHP_FPM_FFMPEG=false
PHP_FPM_AUDIOWAVEFORM=false
PHP_FPM_ADDITIONAL_LOCALES="en_US.UTF-8 es_ES.UTF-8 fr_FR.UTF-8"
PHP_FPM_INSTALL_DOCKER_CLIENT=false
PHP_FPM_DEFAULT_LOCALE=POSIX
PHP_FPM_XDEBUG_PORT=9000
PHP_FPM_INSTALL_EVENT=false
PHP_FPM_INSTALL_DNSUTILS=true
PHP_FPM_INSTALL_POPPLER_UTILS=false

PHP_FPM_PUID=1000
PHP_FPM_PGID=1000

### PHP_FPM_NEW_RELIC #####################################

PHP_FPM_NEW_RELIC=false
PHP_FPM_NEW_RELIC_KEY=0000
PHP_FPM_NEW_RELIC_APP_NAME=app_name

### PHP_WORKER ############################################

PHP_WORKER_INSTALL_BZ2=false
PHP_WORKER_INSTALL_GD=false
PHP_WORKER_INSTALL_XLSWRITER=false
PHP_WORKER_INSTALL_IMAGEMAGICK=false
PHP_WORKER_IMAGEMAGICK_VERSION=latest
PHP_WORKER_INSTALL_GMP=false
PHP_WORKER_INSTALL_GNUPG=false
PHP_WORKER_INSTALL_LDAP=false
PHP_WORKER_INSTALL_PGSQL=false
PHP_WORKER_INSTALL_MONGO=false
PHP_WORKER_INSTALL_BCMATH=false
PHP_WORKER_INSTALL_MEMCACHED=false
# PHP_WORKER_INSTALL_OCI8 Does not work in php5.6 version
PHP_WORKER_INSTALL_OCI8=false
PHP_WORKER_INSTALL_MSSQL=false
PHP_WORKER_INSTALL_PHALCON=false
PHP_WORKER_INSTALL_APCU=false
PHP_WORKER_INSTALL_SOAP=false
PHP_WORKER_INSTALL_ZIP_ARCHIVE=false
PHP_WORKER_INSTALL_MYSQL_CLIENT=false
PHP_WORKER_INSTALL_AMQP=false
PHP_WORKER_INSTALL_GHOSTSCRIPT=false
PHP_WORKER_INSTALL_SWOOLE=false
PHP_WORKER_INSTALL_TAINT=false
PHP_WORKER_INSTALL_FFMPEG=false
PHP_WORKER_INSTALL_AUDIOWAVEFORM=false
PHP_WORKER_INSTALL_CASSANDRA=false
PHP_WORKER_INSTALL_GEARMAN=false
PHP_WORKER_INSTALL_REDIS=false
PHP_WORKER_INSTALL_IMAP=false
PHP_WORKER_INSTALL_XMLRPC=false
PHP_WORKER_INSTALL_SSDB=false
PHP_WORKER_INSTALL_EVENT=false
PHP_WORKER_INSTALL_INTL=true
PHP_WORKER_INSTALL_POPPLER_UTILS=false

PHP_WORKER_PUID=1000
PHP_WORKER_PGID=1000

### NGINX #################################################

NGINX_HOST_HTTP_PORT=80
NGINX_HOST_HTTPS_PORT=443
NGINX_HOST_LOG_PATH=./logs/nginx/
NGINX_SITES_PATH=./nginx/sites/
NGINX_PHP_UPSTREAM_CONTAINER=php-fpm
NGINX_PHP_UPSTREAM_PORT=9000
NGINX_SSL_PATH=./nginx/ssl/

### OpenResty #################################################

OPENRESTY_HOST_HTTP_PORT=80
OPENRESTY_HOST_HTTPS_PORT=443
OPENRESTY_HOST_LOG_PATH=./logs/openresty/
OPENRESTY_SITES_PATH=./openresty/sites/
OPENRESTY_PHP_UPSTREAM_CONTAINER=php-fpm
OPENRESTY_PHP_UPSTREAM_PORT=9000
OPENRESTY_SSL_PATH=./openresty/ssl/

### LARAVEL_HORIZON ################################################

LARAVEL_HORIZON_INSTALL_BZ2=false
LARAVEL_HORIZON_INSTALL_GD=false
LARAVEL_HORIZON_INSTALL_GMP=false
LARAVEL_HORIZON_INSTALL_GNUPG=false
LARAVEL_HORIZON_INSTALL_LDAP=false
LARAVEL_HORIZON_INSTALL_IMAGEMAGICK=false
LARAVEL_HORIZON_IMAGEMAGICK_VERSION=latest
LARAVEL_HORIZON_INSTALL_SOCKETS=false
LARAVEL_HORIZON_INSTALL_YAML=false
LARAVEL_HORIZON_INSTALL_ZIP_ARCHIVE=false
LARAVEL_HORIZON_INSTALL_PHPREDIS=false
LARAVEL_HORIZON_INSTALL_MONGO=false
LARAVEL_HORIZON_INSTALL_CASSANDRA=false
LARAVEL_HORIZON_INSTALL_FFMPEG=false
LARAVEL_HORIZON_INSTALL_AUDIOWAVEFORM=false
LARAVEL_HORIZON_INSTALL_POPPLER_UTILS=false
LARAVEL_HORIZON_PGID=1000
LARAVEL_HORIZON_PUID=1000

### APACHE ################################################

APACHE_HOST_HTTP_PORT=80
APACHE_HOST_HTTPS_PORT=443
APACHE_HOST_LOG_PATH=./logs/apache2
APACHE_SITES_PATH=./apache2/sites
APACHE_PHP_UPSTREAM_CONTAINER=php-fpm
APACHE_PHP_UPSTREAM_PORT=9000
APACHE_PHP_UPSTREAM_TIMEOUT=60
APACHE_DOCUMENT_ROOT=/var/www/
APACHE_SSL_PATH=./apache2/ssl/
APACHE_INSTALL_HTTP2=false
APACHE_FOR_MAC_M1=false

### MYSQL #################################################

# 5.7, 8.0, 8.4, 9.0
MYSQL_VERSION=8.4
MYSQL_DATABASE=default
MYSQL_USER=default
MYSQL_PASSWORD=secret
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=root
MYSQL_ENTRYPOINT_INITDB=./mysql/docker-entrypoint-initdb.d

### CLICKHOUSE #################################################

CLICKHOUSE_VERSION=********
CLICKHOUSE_GOSU_VERSION=1.14
CLICKHOUSE_CUSTOM_CONFIG=./clickhouse/config.xml
CLICKHOUSE_USERS_CUSTOM_CONFIG=./clickhouse/users.xml
CLICKHOUSE_USER=default
CLICKHOUSE_PASSWORD=HAHA
CLICKHOUSE_HTTP_PORT=8123
CLICKHOUSE_CLIENT_PORT=9000
CLICKHOUSE_NATIVE_PORT=9009
CLICKHOUSE_ENTRYPOINT_INITDB=./clickhouse/docker-entrypoint-initdb.d
CLICKHOUSE_HOST_LOG_PATH=./logs/clickhouse

### REDIS #################################################

REDIS_PORT=6379
REDIS_PASSWORD=secret_redis

### REDIS CLUSTER #########################################

REDIS_CLUSTER_PORT_RANGE=7000-7005

### SSDB #################################################

SSDB_PORT=16801

### ZooKeeper #############################################

ZOOKEEPER_PORT=2181

### Percona ###############################################

PERCONA_DATABASE=homestead
PERCONA_USER=homestead
PERCONA_PASSWORD=secret
PERCONA_PORT=3306
PERCONA_ROOT_PASSWORD=root
PERCONA_ENTRYPOINT_INITDB=./percona/docker-entrypoint-initdb.d

### MSSQL #################################################

MSSQL_DATABASE=master
MSSQL_PASSWORD="yourStrong(!)Password"
MSSQL_PORT=1433

### MARIADB ###############################################

MARIADB_VERSION=latest
MARIADB_DATABASE=default
MARIADB_USER=default
MARIADB_PASSWORD=secret
MARIADB_PORT=3306
MARIADB_ROOT_PASSWORD=root
MARIADB_ENTRYPOINT_INITDB=./mariadb/docker-entrypoint-initdb.d

### POSTGRES ##############################################

POSTGRES_VERSION=alpine
POSTGRES_CLIENT_VERSION=15
POSTGRES_DB=default
POSTGRES_USER=default
POSTGRES_PASSWORD=secret
POSTGRES_PORT=5432
POSTGRES_ENTRYPOINT_INITDB=./postgres/docker-entrypoint-initdb.d

### POSTGRES-POSTGIS ##############################################

POSTGIS_VERSION=latest
POSTGIS_INSTALL_PGSQL_HTTP_FOR_POSTGIS13=false

### SQS ##############################################

SQS_NODE_HOST_PORT=9324
SQS_MANAGEMENT_HTTP_HOST_PORT=9325

### RABBITMQ ##############################################

RABBITMQ_NODE_HOST_PORT=5672
RABBITMQ_MANAGEMENT_HTTP_HOST_PORT=15672
RABBITMQ_MANAGEMENT_HTTPS_HOST_PORT=15671
RABBITMQ_WEB_STOMP_HOST_PORT=15674

### MERCURE ##############################################

MERCURE_NODE_HOST_HTTP_PORT=1337
MERCURE_NODE_HOST_HTTPS_PORT=1338
MERCURE_PUBLISHER_JWT_KEY=secret
MERCURE_SUBSCRIBER_JWT_KEY=another_secret
MERCURE_DEBUG=debug
MERCURE_SERVER_NAME=:80

### MEILISEARCH ###########################################

MEILISEARCH_HOST_PORT=7700
MEILISEARCH_KEY=masterkey

### ELASTICSEARCH #########################################

ELASTICSEARCH_HOST_HTTP_PORT=9200
ELASTICSEARCH_HOST_TRANSPORT_PORT=9300

### KIBANA ################################################

KIBANA_HTTP_PORT=5601

### DEJAVU ################################################

DEJAVU_HTTP_PORT=1358

### MEMCACHED #############################################

MEMCACHED_HOST_PORT=11211

### BEANSTALKD CONSOLE ####################################

BEANSTALKD_CONSOLE_BUILD_PATH=./beanstalkd-console
BEANSTALKD_CONSOLE_CONTAINER_NAME=beanstalkd-console
BEANSTALKD_CONSOLE_HOST_PORT=2080

### BEANSTALKD ############################################

BEANSTALKD_HOST_PORT=11300

### SELENIUM ##############################################

SELENIUM_PORT=4444

### MINIO #################################################

MINIO_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_ROOT_USER=laradock
MINIO_ROOT_PASSWORD=laradock

### ADMINER ###############################################

ADM_PORT=8081
ADM_INSTALL_MSSQL=false
ADM_PLUGINS=
ADM_DESIGN=pepa-linha
ADM_DEFAULT_SERVER=mysql

### PHP MY ADMIN ##########################################

# Accepted values: mariadb - mysql

PMA_DB_ENGINE=mysql

# Credentials/Port:

PMA_USER=default
PMA_PASSWORD=secret
PMA_ROOT_PASSWORD=secret
PMA_PORT=8081
PMA_MAX_EXECUTION_TIME=600
PMA_MEMORY_LIMIT=256M
PMA_UPLOAD_LIMIT=2G

### MAILDEV ###############################################

MAILDEV_HTTP_PORT=1080
MAILDEV_SMTP_PORT=25

### VARNISH ###############################################

VARNISH_CONFIG=/etc/varnish/default.vcl
VARNISH_PORT=6081
VARNISH_BACKEND_PORT=81
VARNISHD_PARAMS="-p default_ttl=3600 -p default_grace=3600"

### Varnish ###############################################

# Proxy 1
VARNISH_PROXY1_CACHE_SIZE=128m
VARNISH_PROXY1_BACKEND_HOST=workspace
VARNISH_PROXY1_SERVER=SERVER1

# Proxy 2
VARNISH_PROXY2_CACHE_SIZE=128m
VARNISH_PROXY2_BACKEND_HOST=workspace
VARNISH_PROXY2_SERVER=SERVER2

### HAPROXY ###############################################

HAPROXY_HOST_HTTP_PORT=8085

### JENKINS ###############################################

JENKINS_HOST_HTTP_PORT=8090
JENKINS_HOST_SLAVE_AGENT_PORT=50000
JENKINS_HOME=./jenkins/jenkins_home

### CONFLUENCE ###############################################
CONFLUENCE_POSTGRES_INIT=true
CONFLUENCE_VERSION=6.13-ubuntu-18.04-adoptopenjdk8
CONFLUENCE_POSTGRES_DB=laradock_confluence
CONFLUENCE_POSTGRES_USER=laradock_confluence
CONFLUENCE_POSTGRES_PASSWORD=laradock_confluence
CONFLUENCE_HOST_HTTP_PORT=8090

### GRAFANA ###############################################

GRAFANA_PORT=3000

### GRAYLOG ###############################################

# password must be 16 characters long
GRAYLOG_PASSWORD=somesupersecretpassword
# sha256 representation of the password
GRAYLOG_SHA256_PASSWORD=b1cb6e31e172577918c9e7806c572b5ed8477d3f57aa737bee4b5b1db3696f09
GRAYLOG_PORT=9000
GRAYLOG_SYSLOG_TCP_PORT=514
GRAYLOG_SYSLOG_UDP_PORT=514
GRAYLOG_GELF_TCP_PORT=12201
GRAYLOG_GELF_UDP_PORT=12201

### BLACKFIRE #############################################

# Create an account on blackfire.io. Don't enable blackfire and xDebug at the same time. # visit https://blackfire.io/docs/24-days/06-installation#install-probe-debian for more info.
INSTALL_BLACKFIRE=false
BLACKFIRE_CLIENT_ID="<client_id>"
BLACKFIRE_CLIENT_TOKEN="<client_token>"
BLACKFIRE_SERVER_ID="<server_id>"
BLACKFIRE_SERVER_TOKEN="<server_token>"

### AEROSPIKE #############################################

AEROSPIKE_SERVICE_PORT=3000
AEROSPIKE_FABRIC_PORT=3001
AEROSPIKE_HEARTBEAT_PORT=3002
AEROSPIKE_INFO_PORT=3003
AEROSPIKE_STORAGE_GB=1
AEROSPIKE_MEM_GB=1
AEROSPIKE_NAMESPACE=test

### RETHINKDB #############################################

RETHINKDB_PORT=8090

### MONGODB ###############################################

MONGODB_PORT=27017
MONGO_USERNAME=root
MONGO_PASSWORD=example

### CADDY #################################################

CADDY_HOST_HTTP_PORT=80
CADDY_HOST_HTTPS_PORT=443
CADDY_HOST_LOG_PATH=./logs/caddy
CADDY_CONFIG_PATH=./caddy/caddy

### LARAVEL ECHO SERVER ###################################

LARAVEL_ECHO_SERVER_PORT=6001

### THUMBOR ############################################################################################################

THUMBOR_PORT=8000
THUMBOR_LOG_FORMAT="%(asctime)s %(name)s:%(levelname)s %(message)s"
THUMBOR_LOG_DATE_FORMAT="%Y-%m-%d %H:%M:%S"
MAX_WIDTH=0
MAX_HEIGHT=0
MIN_WIDTH=1
MIN_HEIGHT=1
ALLOWED_SOURCES=[]
QUALITY=80
WEBP_QUALITY=None
PNG_COMPRESSION_LEVEL=6
AUTO_WEBP=False
MAX_AGE=86400
MAX_AGE_TEMP_IMAGE=0
RESPECT_ORIENTATION=False
IGNORE_SMART_ERRORS=False
PRESERVE_EXIF_INFO=False
ALLOW_ANIMATED_GIFS=True
USE_GIFSICLE_ENGINE=False
USE_BLACKLIST=False
LOADER=thumbor.loaders.http_loader
STORAGE=thumbor.storages.file_storage
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
RESULT_STORAGE=thumbor.result_storages.file_storage
ENGINE=thumbor.engines.pil
SECURITY_KEY="MY_SECURE_KEY"
ALLOW_UNSAFE_URL=True
ALLOW_OLD_URLS=True
FILE_LOADER_ROOT_PATH=/data/loader
HTTP_LOADER_CONNECT_TIMEOUT=5
HTTP_LOADER_REQUEST_TIMEOUT=20
HTTP_LOADER_FOLLOW_REDIRECTS=True
HTTP_LOADER_MAX_REDIRECTS=5
HTTP_LOADER_FORWARD_USER_AGENT=False
HTTP_LOADER_DEFAULT_USER_AGENT="Thumbor/5.2.1"
HTTP_LOADER_PROXY_HOST=None
HTTP_LOADER_PROXY_PORT=None
HTTP_LOADER_PROXY_USERNAME=None
HTTP_LOADER_PROXY_PASSWORD=None
HTTP_LOADER_CA_CERTS=None
HTTP_LOADER_VALIDATE_CERTS=True
HTTP_LOADER_CLIENT_KEY=None
HTTP_LOADER_CLIENT_CERT=None
HTTP_LOADER_CURL_ASYNC_HTTP_CLIENT=False
STORAGE_EXPIRATION_SECONDS=2592000
STORES_CRYPTO_KEY_FOR_EACH_IMAGE=False
FILE_STORAGE_ROOT_PATH=/data/storage
UPLOAD_MAX_SIZE=0
UPLOAD_ENABLED=False
UPLOAD_PHOTO_STORAGE=thumbor.storages.file_storage
UPLOAD_DELETE_ALLOWED=False
UPLOAD_PUT_ALLOWED=False
UPLOAD_DEFAULT_FILENAME=image
MONGO_STORAGE_SERVER_HOST=mongo
MONGO_STORAGE_SERVER_PORT=27017
MONGO_STORAGE_SERVER_DB=thumbor
MONGO_STORAGE_SERVER_COLLECTION=images
REDIS_STORAGE_SERVER_HOST=redis
REDIS_STORAGE_SERVER_PORT=6379
REDIS_STORAGE_SERVER_DB=0
REDIS_STORAGE_SERVER_PASSWORD=None
REDIS_RESULT_STORAGE_SERVER_HOST=redis
REDIS_RESULT_STORAGE_SERVER_PORT=6379
REDIS_RESULT_STORAGE_SERVER_DB=0
REDIS_RESULT_STORAGE_SERVER_PASSWORD=None
MEMCACHE_STORAGE_SERVERS=["localhost:11211",]
MIXED_STORAGE_FILE_STORAGE=thumbor.storages.no_storage
MIXED_STORAGE_CRYPTO_STORAGE=thumbor.storages.no_storage
MIXED_STORAGE_DETECTOR_STORAGE=thumbor.storages.no_storage
META_CALLBACK_NAME=None
DETECTORS=[]
FACE_DETECTOR_CASCADE_FILE=haarcascade_frontalface_alt.xml
OPTIMIZERS=[]
JPEGTRAN_PATH=/usr/bin/jpegtran
PROGRESSIVE_JPEG=True
FILTERS="[thumbor.filters.brightness, thumbor.filters.contrast, thumbor.filters.rgb, thumbor.filters.round_corner, thumbor.filters.quality, thumbor.filters.noise, thumbor.filters.watermark, thumbor.filters.equalize, thumbor.filters.fill, thumbor.filters.sharpen, thumbor.filters.strip_icc, thumbor.filters.frame, thumbor.filters.grayscale, thumbor.filters.rotate, thumbor.filters.format, thumbor.filters.max_bytes, thumbor.filters.convolution, thumbor.filters.blur, thumbor.filters.extract_focal, thumbor.filters.no_upscale]"
RESULT_STORAGE_EXPIRATION_SECONDS=0
RESULT_STORAGE_FILE_STORAGE_ROOT_PATH=/data/result_storage
RESULT_STORAGE_STORES_UNSAFE=False
REDIS_QUEUE_SERVER_HOST=redis
REDIS_QUEUE_SERVER_PORT=6379
REDIS_QUEUE_SERVER_DB="0"
REDIS_QUEUE_SERVER_PASSWORD=None
SQS_QUEUE_KEY_ID=None
SQS_QUEUE_KEY_SECRET=None
SQS_QUEUE_REGION=us-east-1
USE_CUSTOM_ERROR_HANDLING=False
ERROR_HANDLER_MODULE=thumbor.error_handlers.sentry
ERROR_FILE_LOGGER=None
ERROR_FILE_NAME_USE_CONTEXT="False"
SENTRY_DSN_URL=
TC_AWS_REGION=eu-west-1
TC_AWS_ENDPOINT=None
TC_AWS_STORAGE_BUCKET=
TC_AWS_STORAGE_ROOT_PATH=
TC_AWS_LOADER_BUCKET=
TC_AWS_LOADER_ROOT_PATH=
TC_AWS_RESULT_STORAGE_BUCKET=
TC_AWS_RESULT_STORAGE_ROOT_PATH=
TC_AWS_STORAGE_SSE=False
TC_AWS_STORAGE_RRS=False
TC_AWS_ENABLE_HTTP_LOADER=False
TC_AWS_ALLOWED_BUCKETS=False
TC_AWS_STORE_METADATA=False

### SOLR ##################################################

SOLR_VERSION=5.5
SOLR_PORT=8983
SOLR_DATAIMPORTHANDLER_MYSQL=false
SOLR_DATAIMPORTHANDLER_MSSQL=false

### GITLAB ###############################################
GITLAB_POSTGRES_INIT=true
GITLAB_HOST_HTTP_PORT=8989
GITLAB_HOST_HTTPS_PORT=9898
GITLAB_HOST_SSH_PORT=2289
GITLAB_DOMAIN_NAME=http://localhost
GITLAB_ROOT_PASSWORD=laradock
GITLAB_HOST_LOG_PATH=./logs/gitlab
*********************postgres
GITLAB_POSTGRES_USER=laradock_gitlab
GITLAB_POSTGRES_PASSWORD=laradock_gitlab
GITLAB_POSTGRES_DB=laradock_gitlab

### GITLAB-RUNNER ###############################################
GITLAB_CI_SERVER_URL=http://localhost:8989
GITLAB_RUNNER_REGISTRATION_TOKEN="<my-registration-token>"
GITLAB_REGISTER_NON_INTERACTIVE=true

### JUPYTERHUB ###############################################
JUPYTERHUB_POSTGRES_INIT=true
JUPYTERHUB_POSTGRES_HOST=postgres
JUPYTERHUB_POSTGRES_USER=laradock_jupyterhub
JUPYTERHUB_POSTGRES_PASSWORD=laradock_jupyterhub
JUPYTERHUB_POSTGRES_DB=laradock_jupyterhub
JUPYTERHUB_PORT=9991
JUPYTERHUB_OAUTH_CALLBACK_URL=http://laradock:9991/hub/oauth_callback
JUPYTERHUB_OAUTH_CLIENT_ID={GITHUB_CLIENT_ID}
JUPYTERHUB_OAUTH_CLIENT_SECRET={GITHUB_CLIENT_SECRET}
JUPYTERHUB_CUSTOM_CONFIG=./jupyterhub/jupyterhub_config.py
JUPYTERHUB_USER_DATA=/jupyterhub
JUPYTERHUB_USER_LIST=./jupyterhub/userlist
JUPYTERHUB_ENABLE_NVIDIA=false

### IPYTHON ##################################################
LARADOCK_IPYTHON_CONTROLLER_IP=127.0.0.1

### NETDATA ###############################################
NETDATA_PORT=19999

### REDISWEBUI #########################################
REDIS_WEBUI_USERNAME=laradock
REDIS_WEBUI_PASSWORD=laradock
REDIS_WEBUI_CONNECT_HOST=redis
REDIS_WEBUI_CONNECT_PORT=6379
REDIS_WEBUI_PORT=9987

### MONGOWEBUI ###############################################
MONGO_WEBUI_PORT=3000
MONGO_WEBUI_ROOT_URL=http://localhost
MONGO_WEBUI_MONGO_URL=mongodb://mongo:27017/
MONGO_WEBUI_INSTALL_MONGO=false

### METABASE ###############################################
METABASE_PORT=3030
METABASE_DB_FILE=metabase.db
METABASE_JAVA_TIMEZONE=US/Pacific

### IDE ###############################################
IDE_THEIA_PORT=987
IDE_WEBIDE_PORT=984
IDE_CODIAD_PORT=985
IDE_ICECODER_PORT=986

### DOCKERREGISTRY ###############################################
DOCKER_REGISTRY_PORT=5000

### DOCKERWEBUI ###############################################
DOCKER_WEBUI_REGISTRY_HOST=docker-registry
DOCKER_WEBUI_REGISTRY_PORT=5000
# if have use https proxy please set to 1
DOCKER_REGISTRY_USE_SSL=0
DOCKER_REGISTRY_BROWSE_ONLY=false
DOCKER_WEBUI_PORT=8754

### MAILU ###############################################
MAILU_VERSION=latest
MAILU_RECAPTCHA_PUBLIC_KEY="<YOUR_RECAPTCHA_PUBLIC_KEY>"
MAILU_RECAPTCHA_PRIVATE_KEY="<YOUR_RECAPTCHA_PRIVATE_KEY>"
# Main mail domain
MAILU_HTTP_PORT=6080
MAILU_HTTPS_PORT=60443
MAILU_DOMAIN=example.com
MAILU_INIT_ADMIN_USERNAME=laradock
MAILU_INIT_ADMIN_PASSWORD=laradock
# Hostnames for this server, separated with comas
MAILU_HOSTNAMES=mail.example.com,alternative.example.com,yetanother.example.com
# Postmaster local part (will append the main mail domain)
MAILU_POSTMASTER=admin
# Set to a randomly generated 16 bytes string
MAILU_SECRET_KEY=ChangeMeChangeMe
# Choose how secure connections will behave (value: letsencrypt, cert, notls, mail)
MAILU_TLS_FLAVOR=cert
# Authentication rate limit (per source IP address)
MAILU_AUTH_RATELIMIT="10/minute;1000/hour"
# Opt-out of statistics, replace with "True" to opt out
MAILU_DISABLE_STATISTICS=False
# Message size limit in bytes
# Default: accept messages up to 50MB
MAILU_MESSAGE_SIZE_LIMIT=50000000
# Will relay all outgoing mails if configured
MAILU_RELAYHOST=
# Networks granted relay permissions, make sure that you include your Docker
# internal network (default to **********/16)
MAILU_RELAYNETS=**********/12
# Fetchmail delay
MAILU_FETCHMAIL_DELAY=600
# Recipient delimiter, character used to delimiter localpart from custom address part
# e.g. localpart+custom@domain;tld
MAILU_RECIPIENT_DELIMITER=+
# DMARC rua and ruf email
MAILU_DMARC_RUA=admin
MAILU_DMARC_RUF=admin
# Welcome email, enable and set a topic and body if you wish to send welcome
# emails to all users.
MAILU_WELCOME=True
MAILU_WELCOME_SUBJECT="Welcome to your new email account"
MAILU_WELCOME_BODY="Welcome to your new email account, if you can read this, then it is configured properly!"
# Path to the admin interface if enabled
MAILU_WEB_ADMIN=/admin
# Path to the webmail if enabled
MAILU_WEB_WEBMAIL=/webmail
# Website name
MAILU_SITENAME="Example Mail"
# Linked Website URL
MAILU_WEBSITE=http://mail.example.com
# Default password scheme used for newly created accounts and changed passwords
# (value: SHA512-CRYPT, SHA256-CRYPT, MD5-CRYPT, CRYPT)
MAILU_PASSWORD_SCHEME=SHA512-CRYPT
# Expose the admin interface (value: true, false)
MAILU_ADMIN=true
# Choose which webmail to run if any (values: roundcube, rainloop, none)
MAILU_WEBMAIL=rainloop
# Dav server implementation (value: radicale, none)
MAILU_WEBDAV=radicale

### TRAEFIK #################################################

TRAEFIK_HOST_HTTP_PORT=80
TRAEFIK_HOST_HTTPS_PORT=443
TRAEFIK_DASHBOARD_PORT=8888
# basic authentication for traefik dashboard username: admin password:admin
TRAEFIK_DASHBOARD_USER='admin:$2y$10$lXaL3lj6raFic6rFqr2.lOBoCudAIhB6zyoqObNg290UFppiUzTTi'
ACME_DOMAIN=example.org
ACME_EMAIL=<EMAIL>

### MOSQUITTO #################################################

MOSQUITTO_PORT=9001

### COUCHDB ###################################################

COUCHDB_PORT=5984

### Manticore Search ##########################################

MANTICORE_CONFIG_PATH=./manticore/config
MANTICORE_API_PORT=9312
MANTICORE_SPHINXQL_PORT=9306
MANTICORE_HTTP_PORT=9308

### pgadmin ##################################################
# use this address http://ip6-localhost:5050
PGADMIN_PORT=5050
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin

### SWAGGER EDITOR  ###########################################

SWAGGER_EDITOR_PORT=5151

### SWAGGER UI  ###############################################

SWAGGER_API_URL=http://generator.swagger.io/api/swagger.json
SWAGGER_UI_PORT=5555

### SONARQUBE ################################################
## docker-compose up -d sonarqube
## (If you encounter a database error)
## docker-compose exec --user=root postgres
## source docker-entrypoint-initdb.d/init_sonarqube_db.sh
## (If you encounter logs error)
## docker-compose run --user=root --rm sonarqube chown sonarqube:sonarqube /opt/sonarqube/logs

SONARQUBE_HOSTNAME=sonar.example.com
SONARQUBE_PORT=9000
SONARQUBE_POSTGRES_INIT=true
SONARQUBE_POSTGRES_HOST=postgres
SONARQUBE_POSTGRES_DB=sonar
SONARQUBE_POSTGRES_USER=sonar
SONARQUBE_POSTGRES_PASSWORD=sonarPass

### TOMCAT ################################################
TOMCAT_VERSION=8.5.43
TOMCAT_HOST_HTTP_PORT=8080

### CASSANDRA ################################################

# Cassandra Version, supported tags can be found at https://hub.docker.com/r/bitnami/cassandra/
CASSANDRA_VERSION=latest
# Inter-node cluster communication port. Default: 7000
CASSANDRA_TRANSPORT_PORT_NUMBER=7000
# JMX connections port. Default: 7199
CASSANDRA_JMX_PORT_NUMBER=7199
# Client port. Default: 9042.
CASSANDRA_CQL_PORT_NUMBER=9042
# Cassandra user name. Defaults: cassandra
CASSANDRA_USER=cassandra
# Password seeder will change the Cassandra default credentials at initialization. In clusters, only one node should be marked as password seeder. Default: no
CASSANDRA_PASSWORD_SEEDER=no
# Cassandra user password. Default: cassandra
CASSANDRA_PASSWORD=cassandra
# Number of tokens for the node. Default: 256.
CASSANDRA_NUM_TOKENS=256
# Hostname used to configure Cassandra. It can be either an IP or a domain. If left empty, it will be resolved to the machine IP.
CASSANDRA_HOST=
# Cluster name to configure Cassandra.. Defaults: My Cluster
CASSANDRA_CLUSTER_NAME="My Cluster"
# : Hosts that will act as Cassandra seeds. No defaults.
CASSANDRA_SEEDS=
 # Snitch name (which determines which data centers and racks nodes belong to). Default SimpleSnitch
CASSANDRA_ENDPOINT_SNITCH=SimpleSnitch
 # Enable the thrift RPC endpoint. Default :true
CASSANDRA_ENABLE_RPC=true
# Datacenter name for the cluster. Ignored in SimpleSnitch endpoint snitch. Default: dc1.
CASSANDRA_DATACENTER=dc1
# Rack name for the cluster. Ignored in SimpleSnitch endpoint snitch. Default: rack1.
CASSANDRA_RACK=rack1

### GEARMAN ##################################################

# Gearman version to use. See available tags at https://hub.docker.com/r/artefactual/gearmand
GEARMAN_VERSION=latest
# Port to use (Default: 4730)
GEARMAN_PORT=4730
# Logging Level (Default: INFO)
GEARMAN_VERBOSE=INFO
# Persistent queue type to use (Default: builtin)
GEARMAN_QUEUE_TYPE=builtin
# Number of I/O threads to use (Default: 4)
GEARMAN_THREADS=4
# Number of backlog connections for listen (Default: 32)
GEARMAN_BACKLOG=32
# Number of file descriptors to allow for the process (Default is max allowed for user)
GEARMAN_FILE_DESCRIPTORS=
# Number of attempts to run the job before the job server removes it. (Default: no limit = 0)
GEARMAN_JOB_RETRIES=0
# Assign work in round-robin order per worker connection (Default: 0)
GEARMAN_ROUND_ROBIN=0
# Number of workers to wakeup for each job received (Default: 0)
GEARMAN_WORKER_WAKEUP=0
# Enable keepalive on sockets (Default: 0)
GEARMAN_KEEPALIVE=0
# The duration between two keepalive transmissions in idle condition (Default: 30)
GEARMAN_KEEPALIVE_IDLE=30
# The duration between two successive keepalive retransmissions, if acknowledgement to the previous keepalive transmission is not received	(Default: 10)
GEARMAN_KEEPALIVE_INTERVAL=10
# The number of retransmissions to be carried out before declaring that remote end is not available (Default: 5)
GEARMAN_KEEPALIVE_COUNT=5
# Mysql server host (Default: localhost)
GEARMAN_MYSQL_HOST=localhost
# Mysql server port (Default: 3306)
GEARMAN_MYSQL_PORT=3306
# Mysql server user (Default: root)
GEARMAN_MYSQL_USER=root
# Mysql password
GEARMAN_MYSQL_PASSWORD=
# Path to file with mysql password(Docker secrets)
GEARMAN_MYSQL_PASSWORD_FILE=
# Database to use by Gearman (Default: Gearmand)
GEARMAN_MYSQL_DB=Gearmand
# Table to use by Gearman (Default: gearman_queue)
GEARMAN_MYSQL_TABLE=gearman_queue

### ELK Stack ##################################################
ELK_VERSION=7.9.1

### Tarantool ##################################################
TARANTOOL_PORT=3301
TARANTOOL_ADMIN_PORT=8002

### NATS ##################################################
NATS_CLIENT_PORT=4222
NATS_MONITORING_PORT=6222
NATS_ROUTE_PORT=8222

### SOKETI ##################################################
SOKETI_NODE_VERSION=16-debian
SOKETI_BASE_IMAGE_TAG_PREFIX=latest
SOKETI_PORT=6001
SOKETI_METRICS_SERVER_PORT=9601

### ONEDEV ##################################################
ONEDEV_HTTP_PORT=6610
ONEDEV_SSH_PORT=6611

### Keycloak ################################################
KEYCLOAK_VERSION=latest
KEYCLOAK_POSTGRES_INIT=true
KEYCLOAK_HTTP_PORT=8081
KEYCLOAK_CREATE_ADMIN_USER=true
KEYCLOAK_ADMIN_USER='admin'
KEYCLOAK_ADMIN_PASSWORD='secret'
KEYCLOAK_POSTGRES_HOST=postgres
KEYCLOAK_POSTGRES_USER=laradock_keycloak
KEYCLOAK_POSTGRES_PASSWORD=laradock_keycloak
KEYCLOAK_POSTGRES_DB=laradock_keycloak

### Mailpit #################################################
MAILPIT_HTTP_PORT=8125
MAILPIT_SMTP_PORT=1125
