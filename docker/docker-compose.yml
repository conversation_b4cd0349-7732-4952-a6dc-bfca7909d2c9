version: '3.8'

services:
  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: buzfi_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../:/var/www/html
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - php-fpm
    networks:
      - buzfi-network

  # PHP-FPM 8.2
  php-fpm:
    build:
      context: ./php
      dockerfile: Dockerfile
    container_name: buzfi_php
    restart: unless-stopped
    volumes:
      - ../:/var/www/html
      - ./php/php.ini:/usr/local/etc/php/php.ini
      - ./php/logs:/var/log/php
    depends_on:
      - mysql
      - redis
    networks:
      - buzfi-network
    environment:
      - PHP_IDE_CONFIG=serverName=buzfi-backend

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: buzfi_mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: buzfi
      MYSQL_USER: buzfi_user
      MYSQL_PASSWORD: buzfi_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/logs:/var/log/mysql
      - ../import_database:/docker-entrypoint-initdb.d
    networks:
      - buzfi-network

  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: buzfi_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
      MYSQL_ROOT_PASSWORD: root
    depends_on:
      - mysql
    networks:
      - buzfi-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: buzfi_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - buzfi-network

  # Redis Commander (Web UI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: buzfi_redis_commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    depends_on:
      - redis
    networks:
      - buzfi-network

networks:
  buzfi-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
