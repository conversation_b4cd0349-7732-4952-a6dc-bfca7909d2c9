#!/bin/bash

# Buz<PERSON>vel Project - Docker Startup Script
# This script provides an easy way to manage the Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project directories
DOCKER_DIR="docker/laradock"
PROJECT_NAME="Buzfi Laravel Project"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if containers are running
check_containers() {
    cd $DOCKER_DIR
    if docker-compose ps | grep -q "Up"; then
        return 0
    else
        return 1
    fi
}

# Function to start the project
start_project() {
    print_status "Starting $PROJECT_NAME..."
    
    check_docker
    
    cd $DOCKER_DIR
    docker-compose up -d nginx php-fpm mysql redis phpmyadmin redis-commander
    
    print_success "Project started successfully!"
    print_status "Waiting for services to be ready..."
    sleep 10
    
    echo ""
    echo -e "${YELLOW}Access URLs:${NC}"
    echo "  - Application: http://localhost/buzfi-new-backend"
    echo "  - phpMyAdmin: http://localhost:8080 (root/root)"
    echo "  - Redis Commander: http://localhost:8081 (admin/admin)"
    echo ""
}

# Function to stop the project
stop_project() {
    print_status "Stopping $PROJECT_NAME..."
    
    cd $DOCKER_DIR
    docker-compose down
    
    print_success "Project stopped successfully!"
}

# Function to restart the project
restart_project() {
    print_status "Restarting $PROJECT_NAME..."
    stop_project
    start_project
}

# Function to show project status
show_status() {
    print_status "Checking $PROJECT_NAME status..."
    
    cd $DOCKER_DIR
    docker-compose ps
    
    if check_containers; then
        echo ""
        echo -e "${GREEN}Project is running!${NC}"
        echo -e "${YELLOW}Access URLs:${NC}"
        echo "  - Application: http://localhost/buzfi-new-backend"
        echo "  - phpMyAdmin: http://localhost:8080"
        echo "  - Redis Commander: http://localhost:8081"
    else
        echo ""
        echo -e "${RED}Project is not running.${NC}"
        echo "Use './docker-start.sh start' to start the project."
    fi
}

# Function to show logs
show_logs() {
    print_status "Showing logs for $PROJECT_NAME..."
    cd $DOCKER_DIR
    docker-compose logs -f
}

# Function to access shell
access_shell() {
    print_status "Accessing PHP container shell..."
    cd $DOCKER_DIR
    docker-compose exec php-fpm bash
}

# Function to run Laravel commands
run_artisan() {
    if [ -z "$2" ]; then
        print_error "Please provide an artisan command. Example: ./docker-start.sh artisan migrate"
        exit 1
    fi
    
    print_status "Running artisan command: $2"
    cd $DOCKER_DIR
    docker-compose exec php-fpm php artisan $2
}

# Function to show help
show_help() {
    echo -e "${BLUE}$PROJECT_NAME - Docker Management Script${NC}"
    echo ""
    echo -e "${YELLOW}Usage:${NC}"
    echo "  ./docker-start.sh [command]"
    echo ""
    echo -e "${YELLOW}Available commands:${NC}"
    echo "  start      Start all Docker containers"
    echo "  stop       Stop all Docker containers"
    echo "  restart    Restart all Docker containers"
    echo "  status     Show container status and access URLs"
    echo "  logs       Show container logs"
    echo "  shell      Access PHP container shell"
    echo "  artisan    Run Laravel artisan commands"
    echo "  help       Show this help message"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  ./docker-start.sh start"
    echo "  ./docker-start.sh artisan migrate"
    echo "  ./docker-start.sh logs"
}

# Main script logic
case "${1:-help}" in
    start)
        start_project
        ;;
    stop)
        stop_project
        ;;
    restart)
        restart_project
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    shell)
        access_shell
        ;;
    artisan)
        run_artisan "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
