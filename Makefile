# Buzfi Laravel Project - Docker Management
# Usage: make [command]

# Default target
.DEFAULT_GOAL := help

# Variables
DOCKER_DIR = docker/laradock
COMPOSE = docker-compose
SERVICES = nginx php-fpm mysql redis phpmyadmin redis-commander

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

## Help
help: ## Show this help message
	@echo "$(BLUE)Buzfi Laravel Project - Docker Commands$(NC)"
	@echo ""
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

## Project Management
start: ## Start all Docker containers
	@echo "$(GREEN)Starting Buzfi project...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) up -d $(SERVICES)
	@echo "$(GREEN)Project started successfully!$(NC)"
	@echo "$(YELLOW)Access URLs:$(NC)"
	@echo "  - Application: http://localhost/buzfi-new-backend"
	@echo "  - phpMyAdmin: http://localhost:8080"
	@echo "  - Redis Commander: http://localhost:8081"

stop: ## Stop all Docker containers
	@echo "$(RED)Stopping Buzfi project...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) down
	@echo "$(RED)Project stopped.$(NC)"

restart: ## Restart all Docker containers
	@echo "$(YELLOW)Restarting Buzfi project...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) down && $(COMPOSE) up -d $(SERVICES)
	@echo "$(GREEN)Project restarted successfully!$(NC)"

build: ## Build Docker containers
	@echo "$(BLUE)Building Docker containers...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) build $(SERVICES)
	@echo "$(GREEN)Build completed!$(NC)"

rebuild: ## Rebuild Docker containers from scratch
	@echo "$(BLUE)Rebuilding Docker containers from scratch...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) build --no-cache $(SERVICES)
	@echo "$(GREEN)Rebuild completed!$(NC)"

## Container Management
logs: ## Show logs for all containers
	cd $(DOCKER_DIR) && $(COMPOSE) logs -f

logs-nginx: ## Show Nginx logs
	cd $(DOCKER_DIR) && $(COMPOSE) logs -f nginx

logs-php: ## Show PHP-FPM logs
	cd $(DOCKER_DIR) && $(COMPOSE) logs -f php-fpm

logs-mysql: ## Show MySQL logs
	cd $(DOCKER_DIR) && $(COMPOSE) logs -f mysql

logs-redis: ## Show Redis logs
	cd $(DOCKER_DIR) && $(COMPOSE) logs -f redis

## Laravel Commands
shell: ## Access PHP container shell
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm bash

artisan: ## Run Laravel Artisan commands (usage: make artisan cmd="migrate")
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan $(cmd)

composer: ## Run Composer commands (usage: make composer cmd="install")
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm composer $(cmd)

migrate: ## Run Laravel migrations
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan migrate

migrate-fresh: ## Fresh migration with seeding
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan migrate:fresh --seed

cache-clear: ## Clear Laravel cache
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan cache:clear
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan config:clear
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan route:clear
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan view:clear

optimize: ## Optimize Laravel application
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan config:cache
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan route:cache
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan view:cache

## Database Management
db-import: ## Import buzfi.sql database
	@echo "$(BLUE)Importing buzfi.sql database...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) exec mysql mysql -u root -proot buzfi < /docker-entrypoint-initdb.d/buzfi.sql
	@echo "$(GREEN)Database imported successfully!$(NC)"

db-backup: ## Backup database
	@echo "$(BLUE)Creating database backup...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) exec mysql mysqldump -u root -proot buzfi > ../backup/buzfi_backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Database backup created!$(NC)"

db-shell: ## Access MySQL shell
	cd $(DOCKER_DIR) && $(COMPOSE) exec mysql mysql -u root -proot buzfi

## Status and Information
status: ## Show container status
	cd $(DOCKER_DIR) && $(COMPOSE) ps

info: ## Show project information
	@echo "$(BLUE)Buzfi Laravel Project Information$(NC)"
	@echo "$(YELLOW)Project Structure:$(NC)"
	@echo "  - Application: http://localhost/buzfi-new-backend"
	@echo "  - phpMyAdmin: http://localhost:8080 (root/root)"
	@echo "  - Redis Commander: http://localhost:8081 (admin/admin)"
	@echo ""
	@echo "$(YELLOW)Database:$(NC)"
	@echo "  - Host: localhost:3306"
	@echo "  - Database: buzfi"
	@echo "  - Username: buzfi_user"
	@echo "  - Password: buzfi_password"
	@echo "  - Root Password: root"
	@echo ""
	@echo "$(YELLOW)Redis:$(NC)"
	@echo "  - Host: localhost:6379"
	@echo "  - Password: secret_redis"

## Cleanup
clean: ## Clean up Docker resources
	@echo "$(RED)Cleaning up Docker resources...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) down -v --remove-orphans
	docker system prune -f
	@echo "$(RED)Cleanup completed!$(NC)"

clean-all: ## Clean up everything including images
	@echo "$(RED)Cleaning up all Docker resources...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) down -v --remove-orphans --rmi all
	docker system prune -a -f
	@echo "$(RED)Complete cleanup finished!$(NC)"

## Development
install: ## Install project dependencies
	@echo "$(BLUE)Installing project dependencies...$(NC)"
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm composer install
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan key:generate
	cd $(DOCKER_DIR) && $(COMPOSE) exec php-fpm php artisan storage:link
	@echo "$(GREEN)Dependencies installed!$(NC)"

setup: ## Complete project setup
	@echo "$(BLUE)Setting up Buzfi project...$(NC)"
	make build
	make start
	sleep 10
	make install
	make migrate
	@echo "$(GREEN)Project setup completed!$(NC)"
	@echo "$(YELLOW)You can now access:$(NC)"
	@echo "  - Application: http://localhost/buzfi-new-backend"
	@echo "  - phpMyAdmin: http://localhost:8080"
	@echo "  - Redis Commander: http://localhost:8081"

.PHONY: help start stop restart build rebuild logs logs-nginx logs-php logs-mysql logs-redis shell artisan composer migrate migrate-fresh cache-clear optimize db-import db-backup db-shell status info clean clean-all install setup
