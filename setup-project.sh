#!/bin/bash

# Buz<PERSON> Project - Initial Setup Script
# This script sets up the project for the first time

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_NAME="Buzfi Laravel Project"
DOCKER_DIR="docker/laradock"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Setup environment file
setup_env() {
    print_status "Setting up environment file..."
    
    if [ ! -f .env ]; then
        if [ -f .env.docker ]; then
            cp .env.docker .env
            print_success "Environment file created from .env.docker"
        elif [ -f .env.example ]; then
            cp .env.example .env
            print_success "Environment file created from .env.example"
        else
            print_error "No environment template found!"
            exit 1
        fi
    else
        print_warning "Environment file already exists, skipping..."
    fi
}

# Build Docker containers
build_containers() {
    print_status "Building Docker containers..."
    cd $DOCKER_DIR
    docker-compose build nginx php-fpm mysql redis phpmyadmin redis-commander
    print_success "Containers built successfully"
    cd ../../
}

# Start containers
start_containers() {
    print_status "Starting Docker containers..."
    cd $DOCKER_DIR
    docker-compose up -d nginx php-fpm mysql redis phpmyadmin redis-commander
    print_success "Containers started successfully"
    cd ../../
    
    print_status "Waiting for services to be ready..."
    sleep 15
}

# Install dependencies
install_dependencies() {
    print_status "Installing PHP dependencies..."
    cd $DOCKER_DIR
    docker-compose exec php-fpm composer install --no-interaction
    print_success "Dependencies installed"
    cd ../../
}

# Generate application key
generate_key() {
    print_status "Generating application key..."
    cd $DOCKER_DIR
    docker-compose exec php-fpm php artisan key:generate --no-interaction
    print_success "Application key generated"
    cd ../../
}

# Create storage link
create_storage_link() {
    print_status "Creating storage link..."
    cd $DOCKER_DIR
    docker-compose exec php-fpm php artisan storage:link
    print_success "Storage link created"
    cd ../../
}

# Run migrations
run_migrations() {
    print_status "Running database migrations..."
    cd $DOCKER_DIR
    
    # Wait for MySQL to be ready
    print_status "Waiting for MySQL to be ready..."
    sleep 10
    
    # Check if database is accessible
    if docker-compose exec mysql mysql -u root -proot -e "USE buzfi;" > /dev/null 2>&1; then
        print_success "Database is accessible"
        docker-compose exec php-fpm php artisan migrate --no-interaction
        print_success "Migrations completed"
    else
        print_warning "Database not ready or buzfi.sql not imported yet"
        print_status "You can run migrations later with: make migrate"
    fi
    
    cd ../../
}

# Set permissions
set_permissions() {
    print_status "Setting proper permissions..."
    cd $DOCKER_DIR
    docker-compose exec php-fpm chmod -R 775 storage bootstrap/cache
    docker-compose exec php-fpm chown -R www-data:www-data storage bootstrap/cache
    print_success "Permissions set"
    cd ../../
}

# Main setup function
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  $PROJECT_NAME Setup${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_docker
    setup_env
    build_containers
    start_containers
    install_dependencies
    generate_key
    create_storage_link
    set_permissions
    run_migrations
    
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}  Setup Complete!${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo -e "${YELLOW}Access URLs:${NC}"
    echo "  🌐 Application: http://localhost/buzfi-new-backend"
    echo "  🗄️  phpMyAdmin: http://localhost:8080 (root/root)"
    echo "  🔴 Redis Commander: http://localhost:8081 (admin/admin)"
    echo ""
    echo -e "${YELLOW}Database Info:${NC}"
    echo "  📊 Host: localhost:3306"
    echo "  🏷️  Database: buzfi"
    echo "  👤 Username: buzfi_user"
    echo "  🔑 Password: buzfi_password"
    echo ""
    echo -e "${YELLOW}Useful Commands:${NC}"
    echo "  make start          # Start containers"
    echo "  make stop           # Stop containers"
    echo "  make status         # Check status"
    echo "  make logs           # View logs"
    echo "  make shell          # Access PHP shell"
    echo "  ./docker-start.sh   # Alternative management script"
    echo ""
    echo -e "${GREEN}Happy coding! 🚀${NC}"
}

# Run main function
main
